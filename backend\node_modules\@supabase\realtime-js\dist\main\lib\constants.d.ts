export declare const DEFAULT_VERSION = "realtime-js/0.0.0-automated";
export declare const VSN: string;
export declare const VERSION = "0.0.0-automated";
export declare const DEFAULT_TIMEOUT = 10000;
export declare const WS_CLOSE_NORMAL = 1000;
export declare enum SOCKET_STATES {
    connecting = 0,
    open = 1,
    closing = 2,
    closed = 3
}
export declare enum CHANNEL_STATES {
    closed = "closed",
    errored = "errored",
    joined = "joined",
    joining = "joining",
    leaving = "leaving"
}
export declare enum CHANNEL_EVENTS {
    close = "phx_close",
    error = "phx_error",
    join = "phx_join",
    reply = "phx_reply",
    leave = "phx_leave",
    access_token = "access_token"
}
export declare enum TRANSPORTS {
    websocket = "websocket"
}
export declare enum CONNECTION_STATE {
    Connecting = "connecting",
    Open = "open",
    Closing = "closing",
    Closed = "closed"
}
//# sourceMappingURL=constants.d.ts.map